<template>
  <div class="order-tab-content" ref="contentRef">
    <div v-if="showSkeleton" class="skeleton-container">
      <div v-for="i in 3" :key="`skeleton-${i}`" class="order-item">
        <WoCard>
          <div class="skeleton-order">
            <div class="skeleton-header">
              <div class="skeleton-order-number"></div>
              <div class="skeleton-status"></div>
            </div>

            <div class="skeleton-goods">
              <div class="skeleton-image"></div>
              <div class="skeleton-content">
                <div class="skeleton-title"></div>
                <div class="skeleton-subtitle"></div>
                <div class="skeleton-price"></div>
              </div>
            </div>

            <div class="skeleton-buttons">
              <div class="skeleton-button"></div>
              <div class="skeleton-button"></div>
            </div>
          </div>
        </WoCard>
      </div>
    </div>

    <div v-if="!showSkeleton && !loading && orderList.length === 0 && finished" class="empty-state">
      <div class="empty-content">
      </div>
    </div>
    <van-list v-else v-model:loading="loading" :finished="finished" loading-text="加载中..."  finished-text="没有更多了" @load="onLoad"
              :immediate-check="false">
      <div v-for="order in orderList" :key="order.id" class="order-item">
        <WoCard>
          <div class="order-header">
            <div class="order-number-container">
              <span class="order-number-text">服务单号：{{ order.afterSaleId || '暂无服务单号'  }}</span>
              <img v-if="order.afterSaleId" src="@/static/images/copy.png" alt="复制" class="copy-icon" @click.stop="copyOrderNumber(order.id)" />
            </div>
            <div class="order-status" >{{ orderState(order.orderState)
              }}</div>
          </div>

          <div class="goods-section">
            <AfterSaleGoodsCard :key="order.id" :item="order" :image-size="75" :min-height="110" :showActions="true" :itemId="order.id">
              <template #actions>
                <WoButton v-for="action in getItemActions(order)" :key="action.key"
                          :type="action.type || 'primary'" size="small" @click.stop="action.handler">
                  {{ action.label }}
                </WoButton>
              </template>
            </AfterSaleGoodsCard>
          </div>
        </WoCard>
      </div>
    </van-list>

    <ExpirationPopup
      v-model:visible="expirationPopupVisible"
      title=""
      main-text="抱歉，订单已过售后申请时效"
      sub-text="商品已超过售后期限，如需售后可联系客服处理"
      confirm-text="确定"
      @close="expirationPopupVisible = false"
      @confirm="expirationPopupVisible = false"
    />
  </div>
</template>

<script setup>
import {ref, computed, onMounted, onUnmounted, nextTick} from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import AfterSaleGoodsCard from '@components/GoodsCommon/AfterSaleGoodsCard.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import useClipboard from 'vue-clipboard3'
import { closeToast, showLoadingToast, showToast } from 'vant'
import orderState from '@/utils/orderState.js'
import {getAfterSalesInfo} from '@/api/interface/order.js'
import { getBizCode } from '@/utils/curEnv.js'
import { useRouter } from 'vue-router'
import ExpirationPopup from "@components/Common/ExpirationPopup/ExpirationPopup.vue";
import {useAfterSalesStore} from "@store/modules/afterSales.js";
import { useOrderAfterSalesActions } from '@/composables/useOrderAfterSalesActions.js';
const { toClipboard } = useClipboard()
const props = defineProps({
  tabType: {
    type: String,
    required: true
  },
  scrollPosition: {
    type: Number,
    default: 0
  }
})

const afterSalesStore = useAfterSalesStore()

const {
  ORDER_STATE,
  expirationPopupVisible,
  getItemActions: getItemActionsComposable,
  handleAfterSalesAction: handleAfterSalesActionComposable
} = useOrderAfterSalesActions()

const router = useRouter()

const emit = defineEmits(['update-scroll'])
const contentRef = ref(null)
const loading = ref(false)
const finished = ref(false)
const orderList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalPage = ref(0)
const error = ref(false)
const finishedText = ref('没有更多了')
const isRefreshing = ref(false)

const getItemActions = (item) => {
  return getItemActionsComposable(item, {
    showAddToCart: false,
    useSubOrderData: false
  })
}

const handleAfterSalesAction = (item, type) => {
  return handleAfterSalesActionComposable(item, type, {
    useSubOrderData: false
  })
}

const showSkeleton = computed(() => {
  return (loading.value && orderList.value.length === 0 && !error.value)
})

const handleScroll = () => {
  if (contentRef.value) {
    const scrollTop = contentRef.value.scrollTop || document.documentElement.scrollTop || document.body.scrollTop
    emit('update-scroll', scrollTop)
  }
}

const copyOrderNumber = async (orderNumber) => {
  try {
    await toClipboard(orderNumber);
    showToast('复制成功');
  } catch (e) {
    console.error(e);
    showToast('复制失败');
  }
}

const onLoad = async () => {
  const params = {
    bizCode: getBizCode('ORDER'),
    pageNum: currentPage.value,
    pageSize: pageSize.value
  }

  showLoadingToast()
  const [err, json] = await getAfterSalesInfo(params)
  closeToast()

  if (!err) {
    currentPage.value++
    loading.value = false

    if (json?.afterSaleList.length > 0) {
      finishedText.value = '没有更多了'
    }

    if (json && json?.afterSaleList.length <= 0) {
      finished.value = true
      return
    }

    const expandedOrders = []
    json.afterSaleList.forEach(order => {
      order.forEach(item => {
        const mergedOrder = {
          ...item,
          skuNumInfoList: item.skuNumInfoList,
          price: item.orderPrice ,
          totalPrice: item.orderPrice
        }

        expandedOrders.push(mergedOrder)
      })
    })
    if (isRefreshing.value) {
      orderList.value = expandedOrders
      isRefreshing.value = false
    } else if (currentPage.value === 2) {
      orderList.value = expandedOrders
    } else {
      orderList.value = [...orderList.value, ...expandedOrders]
    }

    totalPage.value = json.totalPage
    if (currentPage.value > totalPage.value) {
      finished.value = true
    }

    if (currentPage.value === 2 && props.scrollPosition > 0) {
      nextTick(() => {
        if (contentRef.value) {
          contentRef.value.scrollTop = props.scrollPosition
        }
      })
    }
  } else {
    error.value = true
    loading.value = false
    finished.value = true
  }
}
onMounted(() => {
  if (contentRef.value) {
    contentRef.value.addEventListener('scroll', handleScroll)
  } else {
    window.addEventListener('scroll', handleScroll)
  }

  loading.value = true
  onLoad()
})

const refreshData = async () => {
  try {
    isRefreshing.value = true
    orderList.value = []
    currentPage.value = 1
    finished.value = false
    error.value = false
    loading.value = true
    await onLoad()
  } catch (error) {
    console.error('刷新数据失败:', error)
    isRefreshing.value = false
    throw error
  }
}

defineExpose({
  refreshData
})

onUnmounted(() => {
  if (contentRef.value) {
    contentRef.value.removeEventListener('scroll', handleScroll)
  } else {
    window.removeEventListener('scroll', handleScroll)
  }
})

</script>

<style scoped lang="less">
.order-tab-content {
  min-height: calc(100vh - 134px); // 减去搜索头部、tab头部高度
  padding: 10px;
  background: @bg-color-gray;
}

.order-item {
  margin-bottom: 10px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 11px;

  .order-shop {
    font-size: @font-size-14;
    font-weight: bold;
    color: @text-color-primary;
  }

  .order-number-container {
    display: flex;
    align-items: center;
    margin-right: 15px;
    width: 100%;
    overflow: hidden;

    .order-number-text {
      font-size: @font-size-11;
      color: @text-color-secondary;
      margin-right: 3px;
      .ellipsis()
    }

    .copy-icon {
      width: 10px;
      height: 10px;
      cursor: pointer;
    }
  }


  .countdown-container {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    color: @theme-color;

    .countdown-text {
      font-size: @font-size-14;
      font-weight: @font-weight-600;
      margin-right: 4px;
      color: @theme-color;
    }

    .countdown-time {
      font-size: @font-size-14;
      font-weight: @font-weight-600;
      color: @theme-color;
    }
  }


  .order-status {
    flex-shrink: 0;
    font-size: @font-size-14;
    font-weight: @font-weight-600;

    &.status-unpaid {
      color: @theme-color;
    }

    &.status-unshipped {
      color: #2196f3;
    }

    &.status-shipped {
      color: #4caf50;
    }

    &.status-completed {
      color: @text-color-secondary;
    }
  }
}

.goods-section {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.skeleton-container {
  .skeleton-order {
    padding: 0;
  }

  .skeleton-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;

    .skeleton-order-number {
      width: 120px;
      height: 12px;
      background: #f0f0f0;
      border-radius: 6px;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }

    .skeleton-status {
      width: 60px;
      height: 16px;
      background: #f0f0f0;
      border-radius: 8px;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }
  }

  .skeleton-goods {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;

    .skeleton-image {
      width: 75px;
      height: 75px;
      background: #f0f0f0;
      border-radius: 8px;
      margin-right: 12px;
      flex-shrink: 0;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }

    .skeleton-content {
      flex: 1;
      min-height: 75px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .skeleton-title {
        width: 80%;
        height: 16px;
        background: #f0f0f0;
        border-radius: 8px;
        margin-bottom: 8px;
        animation: skeleton-loading 1.5s ease-in-out infinite;
      }

      .skeleton-subtitle {
        width: 60%;
        height: 12px;
        background: #f0f0f0;
        border-radius: 6px;
        margin-bottom: 8px;
        animation: skeleton-loading 1.5s ease-in-out infinite;
      }

      .skeleton-price {
        width: 40%;
        height: 14px;
        background: #f0f0f0;
        border-radius: 7px;
        animation: skeleton-loading 1.5s ease-in-out infinite;
      }
    }
  }

  .skeleton-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    .skeleton-button {
      width: 60px;
      height: 28px;
      background: #f0f0f0;
      border-radius: 14px;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }
  }
}

@keyframes skeleton-loading {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.6;
  }

  100% {
    opacity: 1;
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;

  .empty-content {
    text-align: center;

    .empty-image {
      width: 160px;
      height: 140px;
      margin-bottom: 16px;
    }

    .empty-text {
      font-size: @font-size-14;
      color: @text-color-secondary;
      margin: 0;
    }
  }
}
</style>
